# 🔧 SmartClips Backend Integration Guide

## 📋 **Complete API Endpoint Specifications**

### **URL Processing Endpoints** (Already Implemented)
```python
# backend/main.py - Lines 541-650

@app.post("/validate-url", response_model=URLValidationResponse)
async def validate_video_url(url: str, current_user: User = Depends(get_current_user))

@app.post("/url-metadata")
async def get_url_metadata(url: str, current_user: User = Depends(get_current_user))

@app.post("/process-url", response_model=ProcessedVideo)
async def process_video_url(request: URLProcessRequest, current_user: User = Depends(get_current_user))
```

### **Clip Management Endpoints** (Need Implementation)
```python
# Required for ClipResults.tsx functionality

@app.get("/clips")
async def get_user_clips(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    platform: Optional[str] = None,
    sort_by: str = "created_at",
    current_user: User = Depends(get_current_user)
):
    """Get paginated clips with search and filtering"""
    query = db.query(models.VideoClip).filter(models.VideoClip.user_id == current_user.id)
    
    if search:
        query = query.filter(models.VideoClip.title.ilike(f"%{search}%"))
    if platform:
        query = query.join(models.Video).filter(models.Video.platform == platform)
    
    if sort_by == "vitality_score":
        query = query.order_by(models.VideoClip.vitality_score.desc())
    elif sort_by == "duration":
        query = query.order_by(models.VideoClip.duration)
    elif sort_by == "downloads":
        query = query.order_by(models.VideoClip.download_count.desc())
    else:
        query = query.order_by(models.VideoClip.created_at.desc())
    
    clips = query.offset(skip).limit(limit).all()
    return clips

@app.get("/clips/{clip_id}")
async def get_clip(clip_id: int, current_user: User = Depends(get_current_user)):
    """Get specific clip details"""
    clip = db.query(models.VideoClip).filter(
        models.VideoClip.id == clip_id,
        models.VideoClip.user_id == current_user.id
    ).first()
    
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    return clip

@app.put("/clips/{clip_id}")
async def update_clip(
    clip_id: int,
    clip_data: ClipUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """Update clip title, description, etc."""
    clip = db.query(models.VideoClip).filter(
        models.VideoClip.id == clip_id,
        models.VideoClip.user_id == current_user.id
    ).first()
    
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    for field, value in clip_data.dict(exclude_unset=True).items():
        setattr(clip, field, value)
    
    db.commit()
    return clip

@app.delete("/clips/{clip_id}")
async def delete_clip(clip_id: int, current_user: User = Depends(get_current_user)):
    """Delete a clip"""
    clip = db.query(models.VideoClip).filter(
        models.VideoClip.id == clip_id,
        models.VideoClip.user_id == current_user.id
    ).first()
    
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    # Delete from storage
    storage.delete_file(clip.url)
    
    # Delete from database
    db.delete(clip)
    db.commit()
    
    return {"message": "Clip deleted successfully"}

@app.get("/clips/{clip_id}/download")
async def download_clip(clip_id: int, current_user: User = Depends(get_current_user)):
    """Download clip file"""
    clip = db.query(models.VideoClip).filter(
        models.VideoClip.id == clip_id,
        models.VideoClip.user_id == current_user.id
    ).first()
    
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    # Increment download count
    clip.download_count += 1
    db.commit()
    
    # Return file download response
    return RedirectResponse(url=clip.url)

@app.post("/clips/batch-download")
async def batch_download_clips(
    clip_ids: List[int],
    current_user: User = Depends(get_current_user)
):
    """Create zip file of multiple clips"""
    clips = db.query(models.VideoClip).filter(
        models.VideoClip.id.in_(clip_ids),
        models.VideoClip.user_id == current_user.id
    ).all()
    
    if not clips:
        raise HTTPException(status_code=404, detail="No clips found")
    
    # Create zip file
    zip_url = storage.create_zip_archive(clips)
    
    return {"download_url": zip_url, "clip_count": len(clips)}
```

### **Timeline Editor Endpoints** (Need Implementation)
```python
# Required for ClipEditor.tsx functionality

@app.post("/projects/save")
async def save_project(
    project_data: ProjectSaveRequest,
    current_user: User = Depends(get_current_user)
):
    """Save timeline project"""
    project = models.Project(
        user_id=current_user.id,
        name=project_data.name,
        timeline_data=project_data.timeline_data,
        clips_data=project_data.clips_data
    )
    
    db.add(project)
    db.commit()
    db.refresh(project)
    
    return project

@app.get("/projects/{project_id}")
async def get_project(project_id: int, current_user: User = Depends(get_current_user)):
    """Load timeline project"""
    project = db.query(models.Project).filter(
        models.Project.id == project_id,
        models.Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return project

@app.post("/clips/{clip_id}/text-overlays")
async def add_text_overlay(
    clip_id: int,
    overlay_data: TextOverlayRequest,
    current_user: User = Depends(get_current_user)
):
    """Add text overlay to clip"""
    clip = db.query(models.VideoClip).filter(
        models.VideoClip.id == clip_id,
        models.VideoClip.user_id == current_user.id
    ).first()
    
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    overlay = models.TextOverlay(
        clip_id=clip_id,
        text=overlay_data.text,
        x_position=overlay_data.x_position,
        y_position=overlay_data.y_position,
        font_size=overlay_data.font_size,
        color=overlay_data.color,
        start_time=overlay_data.start_time,
        end_time=overlay_data.end_time
    )
    
    db.add(overlay)
    db.commit()
    db.refresh(overlay)
    
    return overlay

@app.delete("/text-overlays/{overlay_id}")
async def delete_text_overlay(
    overlay_id: int,
    current_user: User = Depends(get_current_user)
):
    """Delete text overlay"""
    overlay = db.query(models.TextOverlay).filter(
        models.TextOverlay.id == overlay_id
    ).first()
    
    if not overlay:
        raise HTTPException(status_code=404, detail="Overlay not found")
    
    # Verify ownership through clip
    clip = db.query(models.VideoClip).filter(
        models.VideoClip.id == overlay.clip_id,
        models.VideoClip.user_id == current_user.id
    ).first()
    
    if not clip:
        raise HTTPException(status_code=403, detail="Access denied")
    
    db.delete(overlay)
    db.commit()
    
    return {"message": "Overlay deleted successfully"}

@app.post("/clips/{clip_id}/export")
async def export_clip(
    clip_id: int,
    export_options: ExportOptionsRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Export clip with overlays and effects"""
    clip = db.query(models.VideoClip).filter(
        models.VideoClip.id == clip_id,
        models.VideoClip.user_id == current_user.id
    ).first()
    
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")
    
    # Get text overlays
    overlays = db.query(models.TextOverlay).filter(
        models.TextOverlay.clip_id == clip_id
    ).all()
    
    # Create export job
    export_job = models.ExportJob(
        user_id=current_user.id,
        clip_id=clip_id,
        quality=export_options.quality,
        format=export_options.format,
        status="queued"
    )
    
    db.add(export_job)
    db.commit()
    db.refresh(export_job)
    
    # Start background export task
    background_tasks.add_task(
        video_processing.export_clip_with_overlays,
        clip, overlays, export_options, export_job.id
    )
    
    return {"export_job_id": export_job.id, "status": "queued"}

@app.get("/exports/{job_id}/status")
async def get_export_status(job_id: int, current_user: User = Depends(get_current_user)):
    """Check export job status"""
    job = db.query(models.ExportJob).filter(
        models.ExportJob.id == job_id,
        models.ExportJob.user_id == current_user.id
    ).first()
    
    if not job:
        raise HTTPException(status_code=404, detail="Export job not found")
    
    return {
        "status": job.status,
        "progress": job.progress,
        "download_url": job.download_url if job.status == "completed" else None,
        "error_message": job.error_message if job.status == "failed" else None
    }
```

### **Support System Endpoints** (Need Implementation)
```python
# Required for Support.tsx functionality

@app.post("/support/tickets")
async def create_support_ticket(
    ticket_data: SupportTicketRequest,
    current_user: User = Depends(get_current_user)
):
    """Create new support ticket"""
    ticket = models.SupportTicket(
        user_id=current_user.id,
        subject=ticket_data.subject,
        message=ticket_data.message,
        category=ticket_data.category,
        priority=ticket_data.priority,
        status="open"
    )
    
    db.add(ticket)
    db.commit()
    db.refresh(ticket)
    
    # Send notification email
    send_support_notification(ticket, current_user)
    
    return ticket

@app.get("/support/tickets")
async def get_user_tickets(current_user: User = Depends(get_current_user)):
    """Get user's support tickets"""
    tickets = db.query(models.SupportTicket).filter(
        models.SupportTicket.user_id == current_user.id
    ).order_by(models.SupportTicket.created_at.desc()).all()
    
    return tickets

@app.post("/support/attachments")
async def upload_support_attachment(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """Upload support attachment"""
    # Validate file
    if file.size > 10 * 1024 * 1024:  # 10MB limit
        raise HTTPException(status_code=413, detail="File too large")
    
    # Save file
    file_url = storage.upload_support_file(file)
    
    return {"file_url": file_url, "filename": file.filename}
```

## 📊 **Database Schema Updates**

### **New Models Required** (Add to models.py)
```python
class Project(Base):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    name = Column(String(255))
    timeline_data = Column(JSON)  # Store timeline configuration
    clips_data = Column(JSON)     # Store clips arrangement
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    user = relationship("User", back_populates="projects")

class TextOverlay(Base):
    __tablename__ = "text_overlays"
    
    id = Column(Integer, primary_key=True, index=True)
    clip_id = Column(Integer, ForeignKey("video_clips.id"))
    text = Column(Text)
    x_position = Column(Float)
    y_position = Column(Float)
    font_size = Column(Integer)
    color = Column(String(7))  # Hex color
    start_time = Column(Float)
    end_time = Column(Float)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    clip = relationship("VideoClip", back_populates="text_overlays")

class ExportJob(Base):
    __tablename__ = "export_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    clip_id = Column(Integer, ForeignKey("video_clips.id"))
    quality = Column(String(20))
    format = Column(String(10))
    status = Column(String(20))  # queued, processing, completed, failed
    progress = Column(Integer, default=0)
    download_url = Column(String(500))
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    user = relationship("User")
    clip = relationship("VideoClip")

class SupportTicket(Base):
    __tablename__ = "support_tickets"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    subject = Column(String(255))
    message = Column(Text)
    category = Column(String(50))
    priority = Column(String(20))
    status = Column(String(20))  # open, in-progress, resolved, closed
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    user = relationship("User", back_populates="support_tickets")

# Update existing models
class VideoClip(Base):
    # Add new fields
    vitality_score = Column(Integer, default=0)
    download_count = Column(Integer, default=0)
    platform = Column(String(50))
    
    # Add relationships
    text_overlays = relationship("TextOverlay", back_populates="clip")

class User(Base):
    # Add relationships
    projects = relationship("Project", back_populates="user")
    support_tickets = relationship("SupportTicket", back_populates="user")
```

### **Pydantic Models** (Add to main.py)
```python
class ClipUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None

class ProjectSaveRequest(BaseModel):
    name: str
    timeline_data: dict
    clips_data: dict

class TextOverlayRequest(BaseModel):
    text: str
    x_position: float
    y_position: float
    font_size: int
    color: str
    start_time: float
    end_time: float

class ExportOptionsRequest(BaseModel):
    quality: str = "1080p"
    format: str = "mp4"

class SupportTicketRequest(BaseModel):
    subject: str
    message: str
    category: str
    priority: str = "medium"
```

## 🚀 **Implementation Priority**

### **Week 1: Core Functionality**
1. Implement clip management endpoints
2. Add database migrations for new tables
3. Set up file storage for exports

### **Week 2: Advanced Features**
1. Implement timeline editor endpoints
2. Add text overlay system
3. Set up export processing

### **Week 3: Support & Polish**
1. Implement support system
2. Add comprehensive error handling
3. Set up monitoring and logging

**All frontend components are ready and waiting for these backend implementations!** 🎯
